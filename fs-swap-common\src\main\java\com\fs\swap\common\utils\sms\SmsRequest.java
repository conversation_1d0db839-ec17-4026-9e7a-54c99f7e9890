package com.fs.swap.common.utils.sms;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fs.swap.common.enums.SmsTemplateType;

/**
 * 短信发送请求对象
 * 基于创蓝云智最新API参数结构
 * 文档地址：https://doc.chuanglan.com/document/HAQYSZKH9HT5Z50L
 */
public class SmsRequest {

    /**
     * API账号，登录控制台获取
     */
    private String account;

    /**
     * API密码，未开启加密鉴权时则为必传，登录控制台获取
     */
    private String password;

    /**
     * 32位随机字符串
     */
    private String nonce;

    /**
     * 秒级时间戳
     */
    private String timestamp;

    /**
     * 接收的手机号；多个手机号使用英文逗号间隔，一次不要超过1000个
     */
    private String phoneNumbers;

    /**
     * 变量参数值，变量模板必传。短信模板变量对应的实际值，JSON格式
     */
    private String templateParamJson;

    /**
     * 模版Id，创建模板时返回的模板Id
     */
    private String templateId;

    /**
     * 短信签名，如果之前报备的模板没有选择关联签名，则为必传
     */
    private String signature;

    /**
     * 如您需要状态回执，则需要传"true",不传默认为"false"
     */
    private String report;

    /**
     * 状态回执的回调地址，请传入完整带http开头的地址
     */
    private String callbackUrl;

    /**
     * 自定义参数，如订单号或短信发送记录流水号，最大支持64位，状态回执会回传
     */
    private String uid;

    /**
     * 下发短信号码扩展码，用于匹配上行回复，一般5位以内(只支持传数字)
     */
    private String extend;

    public SmsRequest() {
    }

    /**
     * 构造函数
     *
     * @param account      API账号
     * @param password     API密码
     * @param phoneNumbers 手机号码
     */
    public SmsRequest(String account, String password, String phoneNumbers, SmsTemplateType smsTemplateType) {
        this.account = account;
        this.password = password;
        this.phoneNumbers = phoneNumbers;
        this.templateId = smsTemplateType.getTemplateId();
        this.timestamp = String.valueOf(System.currentTimeMillis() / 1000); // 当前时间戳（秒）
        this.nonce = generateNonce(); // 生成32位随机字符串
        this.signature = smsTemplateType.getSignature();
    }

    /**
     * 生成32位随机字符串
     */
    private String generateNonce() {
        String chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 32; i++) {
            sb.append(chars.charAt((int) (Math.random() * chars.length())));
        }
        return sb.toString();
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getNonce() {
        return nonce;
    }

    public void setNonce(String nonce) {
        this.nonce = nonce;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getPhoneNumbers() {
        return phoneNumbers;
    }

    public void setPhoneNumbers(String phoneNumbers) {
        this.phoneNumbers = phoneNumbers;
    }

    public String getTemplateParamJson() {
        return templateParamJson;
    }

    public void setTemplateParamJson(String templateParamJson) {
        this.templateParamJson = templateParamJson;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getReport() {
        return report;
    }

    public void setReport(String report) {
        this.report = report;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }
} 