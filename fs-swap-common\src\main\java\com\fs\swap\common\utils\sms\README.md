# 短信发送工具类使用说明

## 概述

本工具类基于创蓝云智短信API最新版本，为二手交易小程序提供短信发送功能。支持多种业务场景的短信模板，包括下单通知、订单完成、关注用户发布新商品等。

**文档地址**: [https://doc.chuanglan.com/document/HAQYSZKH9HT5Z50L](https://doc.chuanglan.com/document/HAQYSZKH9HT5Z50L)

## 🔄 重要更新说明

创蓝云智API已全面升级，主要变化：

### API参数结构变化
- **新增**: `nonce`（32位随机字符串）、`timestamp`（秒级时间戳）
- **变更**: `phoneNumbers`（替代原params字段）、`templateId`（模板ID）、`templateParamJson`（JSON格式变量）
- **移除**: 旧版本的 `msg`、`params` 等字段

### 响应格式变化
- **成功码**: 从 `"0"` 变更为 `"000000"`
- **字段变更**: `msgId`（替代msgid）、数值类型改为字符串类型

## 配置说明

### 1. 配置文件设置

在 `application-common.yml` 或相应环境的配置文件中添加短信服务配置：

```yaml
# 短信服务配置
sms:
  chuanglan:
    enabled: true # 是否启用短信服务，默认启用，可在开发/测试环境设置为false
    account: your_account # 创蓝短信账号
    password: your_password # 创蓝短信密码
    encrypt: false # 是否开启加密鉴权，开启后使用HmacSHA256签名，默认false
```

**配置说明：**
- `enabled`: 控制短信服务开关，当设置为 `false` 时，短信发送将被跳过并返回模拟响应
- `account`: 创蓝短信API账号
- `password`: 创蓝短信API密码
- `encrypt`: 是否开启加密鉴权，开启后使用HmacSHA256签名，不传递明文密码

**重要提醒：**
- 需要在创蓝控制台完成企业认证+实名认证
- 需要在控制台绑定服务器公网出口IP，否则接口会拦截请求
- 短信签名和模板需要提前申请并审核通过

### 2. 加密鉴权说明

#### 未开启加密鉴权（encrypt: false）
- 直接在请求body中传递password
- Header中X-QA-Hmac-Signature传空字符串

#### 开启加密鉴权（encrypt: true）
- 使用HmacSHA256生成签名
- 签名算法：`HmacSHA256(md5(password), sorted(md5(password) + timestamp + nonce))`
- 通过Header X-QA-Hmac-Signature传递签名
- 请求body中不传递password字段

### 3. 依赖添加

确保在 `fs-swap-common/pom.xml` 中包含以下依赖：

```xml
<!-- OkHttps for HTTP requests -->
<dependency>
    <groupId>cn.zhxu</groupId>
    <artifactId>okhttps-jackson</artifactId>
</dependency>
```

### 4. 异步配置

短信异步线程池已集成在 `fs-swap-framework` 的 `ThreadPoolConfig.java` 中，无需额外配置。线程池配置详情：
- 核心线程数：2
- 最大线程数：10  
- 队列容量：100
- 线程名前缀：SMS-Task-

如需自定义线程池配置，可修改 `ThreadPoolConfig.java` 中的 `smsTaskExecutor()` 方法。

## 短信模板类型

系统预定义了以下短信模板类型，使用创蓝云智规范的变量格式 `{s}`：

| 类型 | 模板ID | 描述 | 模板内容 |
|------|--------|------|----------|
| ORDER_CREATED | 1021001 | 下单通知 | 【趣换网络】您好，您发布的商品《{s}》已被用户 {s} 下单，请等待买家通过您预留的联系方式联系您。 |
| ORDER_COMPLETED_BUY | 1021002 | 订单完成通知(买家) | 【趣换网络】您好，您购买的商品《{s}》已与用户 {s} 完成交易，交易状态已更新为交易成功。如有疑问，可联系平台客服。 |
| ORDER_COMPLETED_SELL | 1021003 | 订单完成通知(卖家) | 【趣换网络】您好，您的商品《{s}》已与用户 {s} 完成交易，交易状态已更新为交易成功。如有疑问，可联系平台客服。 |
| FOLLOW_USER_NEW_PRODUCT | 1021004 | 关注用户发布新商品通知 | 【趣换网络】您关注的用户发布了新商品：{s}，快来看看吧！ |
| ORDER_CANCELLED | 1021005 | 订单取消通知 | 【趣换网络】订单已取消，商品：{s}，如有疑问请联系客服。 |
| REFUND_APPLY | 1021006 | 退款申请通知 | 【趣换网络】您收到一条退款申请，订单商品：{s}，请及时处理。 |
| REFUND_SUCCESS | 1021007 | 退款成功通知 | 【趣换网络】退款已成功，商品：{s}，退款金额：{s}元。 |

**注意：** 
- 变量格式使用创蓝云智规范的 `{s}` 格式
- 每个模板都有对应的模板ID，系统会自动使用
- 变量参数通过templateParamJson以JSON格式传递

## API接口说明

### 接口地址
- 模板Id发送方式接口：`https://smssh.253.com/msg/sms/v2/tpl/send`

### 请求方式
- Content-Type：`application/json`
- 编码格式：`utf-8`
- 请求方式：`POST`

### 加密算法（可选）
```java
public String makeSignature(String md5Password, String timestamp, String nonce) {
   String str = generateStr(md5Password, timestamp, nonce);
    return HmacUtils.hmacSha256Hex(md5Password, str.replaceAll("\\s+", ""));
} 

/**
 * 签名待处理的字符串拼接
 */
public static String generateStr(String md5Password, String timestamp, String nonce){
    String[] array = new String[] { md5Password, timestamp, nonce};
    StringBuffer sb = new StringBuffer();
    // 字符串排序
    Arrays.sort(array);
    for (int i = 0; i < 3; i++) {
        sb.append(array[i]);
    }
    return sb.toString();
}
```

### Request Header
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Content-Type | String | 是 | application/json |
| X-QA-Hmac-Signature | String | 可选 | 使用加密鉴权时传递签名，未开启时传空字符串 |

### 请求参数（最新版本）
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account | String | 是 | API账号，登录控制台获取 |
| password | String | 否 | API密码，未开启加密鉴权时则为必传 |
| nonce | String | 是 | 32位随机字符串 |
| timestamp | String | 是 | 秒级时间戳 |
| phoneNumbers | String | 是 | 接收的手机号；多个手机号使用英文逗号间隔，一次不要超过1000个 |
| templateParamJson | String | 否 | 变量参数值，变量模板必传。JSON格式，参数按param1、param2顺序填充 |
| templateId | String | 是 | 模版Id，创建模板时返回的模板Id |
| signature | String | 否 | 短信签名，如果模板没有关联签名则为必传 |
| report | String | 否 | 状态回执，传"true"获取状态回执，默认"false" |
| callbackUrl | String | 否 | 状态回执的回调地址 |
| uid | String | 否 | 自定义参数，最大支持64位，状态回执会回传 |
| extend | String | 否 | 扩展码，5位以内数字，用于匹配上行回复 |

### 响应参数（最新版本）
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | String | 状态码，"000000"表示成功 |
| errorMsg | String | 错误信息，成功时为空 |
| msgId | String | 消息ID（32位纯数字） |
| successNum | String | 成功条数 |
| failNum | String | 失败条数 |
| time | String | 响应时间 |

### 请求示例
```json
{
    "account": "N6000001",
    "timestamp": "**********",
    "nonce": "x4zfk0y5foqwx6cbnw3bfmimy98abqs1",
    "phoneNumbers": "***********,***********",
    "templateId": "1021001",
    "templateParamJson": "[{\"param1\":\"张1\",\"param2\":\"张2\"},{\"param1\":\"李1\",\"param2\":\"李2\"}]",
    "signature": "【创蓝云智】",
    "report": "true",
    "uid": "test_001",
    "extend": "01"
}
```

### 响应示例
```json
{
    "code": "000000",
    "failNum": "0",
    "successNum": "2",
    "msgId": "25071018345400902898000000000001",
    "time": "**************",
    "errorMsg": ""
}
```

## 错误码说明

常见错误码及处理方式：

| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 000000 | 提交成功 | - |
| 101 | 无此用户 | 检查账号配置 |
| 102 | 密码错 | 检查密码配置 |
| 109 | 无发送额度 | 联系商务充值 |
| 114 | 可用参数组个数错误 | 检查变量格式是否使用{$var} |
| 116 | 签名不合法或未带签名 | 检查短信签名是否审核通过 |
| 117 | IP地址认证错 | 在控制台添加IP白名单 |
| 124 | 白模板匹配错误 | 检查短信内容是否与审核模板一致 |
| 129 | JSON格式错误 | 检查请求参数JSON格式 |
| 139 | 超出安全发送时间 | 时间戳过期，确保时间戳差异在30s内 |
| 152 | 模板不存在 | 检查模板ID是否正确 |

完整错误码列表请参考创蓝云智官方文档。

## 使用方法

### 1. 注入工具类

```java
@Autowired
private SmsUtils smsUtils;
```

### 2. 同步发送短信

#### 简单发送（不带业务ID）
```java
SmsResponse response = smsUtils.sendSimpleSms(
    "13800138000", 
    SmsTemplateType.ORDER_COMPLETED, 
    "iPhone 13 Pro"
);
```

#### 带业务ID发送
```java
SmsResponse response = smsUtils.sendSms(
    "13800138000", 
    SmsTemplateType.ORDER_CREATED, 
    "ORDER202401001", // 业务ID
    "张三", // 买家姓名
    "iPhone 13 Pro" // 商品名称
);
```

### 3. 异步发送短信（推荐）

#### 异步发送无需关心结果
```java
// 异步发送下单通知
smsUtils.sendSmsAsyncVoid("13800138000", SmsTemplateType.ORDER_CREATED, "ORDER001", "张三", "iPhone 13");

// 异步发送订单完成通知（买家）
smsUtils.sendSmsAsyncVoid("13900139000", SmsTemplateType.ORDER_COMPLETED_BUY, "ORDER002", "小米笔记本");

// 异步发送退款成功通知
smsUtils.sendSmsAsyncVoid("13700137000", SmsTemplateType.REFUND_SUCCESS, "ORDER003", "MacBook Pro", "8888.00");
```

#### 异步发送需要处理结果
```java
CompletableFuture<SmsResponse> future = smsUtils.sendSmsAsync(
    "13800138000", 
    SmsTemplateType.ORDER_CREATED, 
    "ORDER001",
    "张三", 
    "iPhone 13"
);

// 处理异步结果
future.whenComplete((response, throwable) -> {
    if (throwable != null) {
        log.error("短信发送异常", throwable);
    } else if (response.isSuccess()) {
        log.info("短信发送成功，消息ID: {}", response.getMsgid());
    } else {
        log.error("短信发送失败: {}", response.getErrorMsg());
    }
});
```

#### 异步批量发送
```java
List<String> phoneAndVariables = Arrays.asList(
    "13800138001,戴森吹风机",
    "13800138002,戴森吹风机"
);

CompletableFuture<SmsResponse> future = smsUtils.sendBatchSmsAsync(
    phoneAndVariables, 
    SmsTemplateType.FOLLOW_USER_NEW_PRODUCT,
    "PROD001"
);

// 等待结果（可选）
future.thenAccept(response -> {
    if (response != null && response.isSuccess()) {
        log.info("批量通知发送成功，成功条数: {}", response.getSuccessNum());
    }
});
```

### 4. 批量发送短信

```java
List<String> phoneAndVariables = Arrays.asList(
    "13800138001,戴森吹风机",
    "13800138002,戴森吹风机",
    "13800138003,戴森吹风机"
);

SmsResponse response = smsUtils.sendBatchSms(
    phoneAndVariables, 
    SmsTemplateType.FOLLOW_USER_NEW_PRODUCT,
    "PROD202401001"
);
```

### 5. 使用便捷方法构建参数

工具类提供了一些便捷方法来构建常用的参数字符串：

```java
// 订单通知参数
String orderParams = SmsUtils.buildOrderNotifyParams("13800138000", "张三", "iPhone");

// 关注通知参数  
String followParams = SmsUtils.buildFollowNotifyParams("13900139000", "MacBook");

// 退款通知参数
String refundParams = SmsUtils.buildRefundNotifyParams("13700137000", "iPad", "3000.00");
```

## 响应处理

### 响应对象说明

```java
public class SmsResponse {
    private String code;        // 状态码（0为成功）
    private String errorMsg;    // 状态码说明
    private String msgid;       // 消息ID
    private Integer successNum; // 成功条数
    private Integer failNum;    // 失败条数
    private String time;        // 响应时间
}
```

### 判断发送结果

```java
```

## 业务场景示例

### 1. 下单时通知卖家

```java
public void notifySellerOnOrder(String sellerPhone, String buyerName, String productName, String orderId) {
    try {
        SmsResponse response = smsUtils.sendSms(
            sellerPhone, 
            SmsTemplateType.ORDER_CREATED, 
            orderId,
            buyerName, 
            productName
        );
        
        if (response.isSuccess()) {
            log.info("下单通知发送成功，订单ID: {}", orderId);
        } else {
            log.error("下单通知发送失败，订单ID: {}, 错误: {}", orderId, response.getErrorMsg());
        }
    } catch (Exception e) {
        log.error("发送下单通知异常，订单ID: {}", orderId, e);
    }
}
```

### 2. 订单完成时通知买家

```java
public void notifyBuyerOnOrderCompleted(String buyerPhone, String productName, String orderId) {
    try {
        SmsResponse response = smsUtils.sendSms(
            buyerPhone, 
            SmsTemplateType.ORDER_COMPLETED, 
            orderId,
            productName
        );
        
        if (response.isSuccess()) {
            log.info("订单完成通知发送成功，订单ID: {}", orderId);
        }
    } catch (Exception e) {
        log.error("发送订单完成通知异常，订单ID: {}", orderId, e);
    }
}
```

### 3. 关注用户发布新商品时批量通知

```java
public void notifyFollowersOnNewProduct(List<String> followerPhones, String productName, String productId) {
    List<String> phoneAndVariables = followerPhones.stream()
        .map(phone -> phone + "," + productName)
        .collect(Collectors.toList());
    
    try {
        SmsResponse response = smsUtils.sendBatchSms(
            phoneAndVariables, 
            SmsTemplateType.FOLLOW_USER_NEW_PRODUCT,
            productId
        );
        
        if (response.isSuccess()) {
            log.info("关注通知批量发送成功，商品ID: {}, 成功条数: {}", productId, response.getSuccessNum());
        }
    } catch (Exception e) {
        log.error("发送关注通知异常，商品ID: {}", productId, e);
    }
}
```

## 注意事项

1. **推荐使用异步发送**：为了不阻塞主业务流程，强烈推荐使用异步方式发送短信
   - 对于不关心发送结果的场景：使用 `AsyncSmsService` 的 `xxxAsync()` 方法
   - 对于需要处理发送结果的场景：使用 `SmsUtils` 的 `xxxAsync()` 方法返回 `CompletableFuture`
2. **短信服务开关**：通过 `sms.chuanglan.enabled` 配置控制短信发送功能
   - 设置为 `true`：正常发送短信
   - 设置为 `false`：跳过发送，返回模拟成功响应，适用于开发/测试环境
3. **配置必须设置**：确保在配置文件中正确设置创蓝短信的账号和密码
4. **模板变量对应**：发送时的变量参数要与模板中的变量数量一致
5. **异常处理**：异步方法已内置异常处理，会记录错误日志
6. **日志记录**：工具类会自动记录发送日志，便于问题排查
7. **批量发送限制**：注意单次批量发送的数量限制，避免超出API限制
8. **环境配置建议**：
   - 开发环境：建议设置 `enabled: false`，避免测试时发送真实短信
   - 测试环境：可根据需要设置，通常也建议设置为 `false`
   - 生产环境：设置 `enabled: true`，确保短信功能正常
9. **线程池监控**：生产环境建议监控短信线程池的运行状态，防止任务积压

## API文档参考

- 创蓝云智短信API文档：https://chuanglan.apifox.cn/doc-357777 