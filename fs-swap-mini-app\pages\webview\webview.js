Page({
  data: {
    url: '',
    title: 'web浏览器'
  },

  onLoad(options) {
    const { url, title } = options
    
    if (url) {
      const decodedUrl = decodeURIComponent(url)
      this.setData({
        url: decodedUrl
      })
    }

    if (title) {
      const decodedTitle = decodeURIComponent(title)
      this.setData({
        title: decodedTitle
      })
      
      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: decodedTitle
      })
    }
  },

  onShow() {
    // 页面显示时的处理
  },

  onMessage(e) {
    // 接收 web-view 发送的消息
    console.log('webview message:', e.detail.data)
  }
}) 