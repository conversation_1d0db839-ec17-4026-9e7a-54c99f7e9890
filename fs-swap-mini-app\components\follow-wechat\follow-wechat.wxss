.follow-wechat {
  position: fixed;
  bottom: 10rpx;
  left: 0;
  right: 0;
  z-index: 999;
  padding: 0 60rpx;
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.follow-wechat-content {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(23, 23, 26, 0.65);
  border-radius: 999rpx;
  padding: 14rpx 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.03);
  max-width: 600rpx;
  margin: 0 auto;
}

.close-icon {
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx !important;
  margin-right: 8rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 4rpx;
}

.close-icon:active {
  transform: scale(0.9);
  opacity: 0.5;
}

.tip-text {
  flex: 1;
  text-align: center;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.75);
  margin: 0 16rpx;
  font-weight: 400;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
  letter-spacing: 0.3px;
}

.follow-btn {
  background: linear-gradient(135deg, rgba(66, 210, 113, 0.85) 0%, rgba(40, 185, 87, 0.85) 100%);
  color: rgba(255, 255, 255, 0.9);
  font-size: 22rpx;
  padding: 10rpx 32rpx;
  border-radius: 999rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(42, 191, 94, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: 0.5px;
  border: 1px solid rgba(255, 255, 255, 0.06);
  white-space: nowrap;
}

.follow-btn:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(42, 191, 94, 0.1);
  opacity: 0.8;
} 