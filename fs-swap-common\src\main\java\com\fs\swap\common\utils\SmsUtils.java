package com.fs.swap.common.utils;

import cn.zhxu.okhttps.OkHttps;
import com.fs.swap.common.enums.SmsTemplateType;
import com.fs.swap.common.exception.ServiceException;
import com.fs.swap.common.utils.sms.SmsRequest;
import com.fs.swap.common.utils.sms.SmsResponse;
import lombok.Getter;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.codec.digest.HmacUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 短信发送工具类
 * 基于创蓝云智短信API最新版本
 * 文档地址：https://doc.chuanglan.com/document/HAQYSZKH9HT5Z50L
 */
@Component
public class SmsUtils {
    
    private static final Logger log = LoggerFactory.getLogger(SmsUtils.class);
    
    /**
     * 创蓝短信API地址 - 最新版本
     * 模板Id发送方式接口
     */
    private static final String SMS_API_URL = "https://smssh.253.com/msg/sms/v2/tpl/send";

    /**
     * -- GETTER --
     *  检查短信服务是否启用
     *
     * @return true-启用，false-禁用
     */
    @Getter
    @Value("${sms.chuanglan.enabled:true}")
    private boolean enabled;
    
    @Value("${sms.chuanglan.account:}")
    private String account;
    
    @Value("${sms.chuanglan.password:}")
    private String password;
    
    @Value("${sms.chuanglan.encrypt:false}")
    private boolean encrypt;
    
    // ========== 同步方法 ==========
    
    /**
     * 发送单条变量短信（同步）
     *
     * @param phone 手机号码
     * @param templateType 短信模板类型
     * @param variables 变量参数
     * @return 发送结果
     */
    public SmsResponse sendSimpleSms(String phone, SmsTemplateType templateType, String... variables) {
        return sendSms(phone, templateType, null, variables);
    }
    
    /**
     * 发送单条变量短信（同步）
     *
     * @param phone 手机号码
     * @param templateType 短信模板类型
     * @param uid 业务系统内的ID
     * @param variables 变量参数
     * @return 发送结果
     */
    public SmsResponse sendSms(String phone, SmsTemplateType templateType, String uid, String... variables) {
        if (StringUtils.isEmpty(phone)) {
            throw new IllegalArgumentException("手机号码不能为空");
        }
        if (templateType == null) {
            throw new IllegalArgumentException("短信模板类型不能为空");
        }
        
        // 构建模板参数JSON
        String templateParamJson = buildTemplateParamJson(variables);
        
        SmsRequest request = new SmsRequest(account, password, phone, templateType);
        request.setTemplateParamJson(templateParamJson);
        request.setReport("true"); // 需要状态报告
        if (StringUtils.isNotEmpty(uid)) {
            request.setUid(uid);
        }
        
        return sendSmsRequest(request);
    }
    
    /**
     * 批量发送变量短信（同步）
     *
     * @param phoneAndVariables 手机号和变量列表，每个元素格式：手机号,变量1,变量2...
     * @param templateType 短信模板类型
     * @return 发送结果
     */
    public SmsResponse sendBatchSms(List<String> phoneAndVariables, SmsTemplateType templateType) {
        return sendBatchSms(phoneAndVariables, templateType, null);
    }
    
    /**
     * 批量发送变量短信（同步）
     *
     * @param phoneAndVariables 手机号和变量列表，每个元素格式：手机号,变量1,变量2...
     * @param templateType 短信模板类型
     * @param uid 业务系统内的ID
     * @return 发送结果
     */
    public SmsResponse sendBatchSms(List<String> phoneAndVariables, SmsTemplateType templateType, String uid) {
        if (phoneAndVariables == null || phoneAndVariables.isEmpty()) {
            throw new IllegalArgumentException("手机号和变量列表不能为空");
        }
        if (templateType == null) {
            throw new IllegalArgumentException("短信模板类型不能为空");
        }
        
        // 解析手机号和变量参数
        StringBuilder phoneNumbers = new StringBuilder();
        StringBuilder templateParamJson = new StringBuilder("[");
        
        for (int i = 0; i < phoneAndVariables.size(); i++) {
            String[] parts = phoneAndVariables.get(i).split(",");
            if (parts.length > 0) {
                if (i > 0) {
                    phoneNumbers.append(",");
                    templateParamJson.append(",");
                }
                phoneNumbers.append(parts[0]); // 手机号
                
                // 构建变量参数JSON对象
                templateParamJson.append("{");
                for (int j = 1; j < parts.length; j++) {
                    if (j > 1) templateParamJson.append(",");
                    templateParamJson.append("\"param").append(j).append("\":\"").append(parts[j]).append("\"");
                }
                templateParamJson.append("}");
            }
        }
        templateParamJson.append("]");
        
        SmsRequest request = new SmsRequest(account, password, phoneNumbers.toString(), templateType);
        request.setTemplateParamJson(templateParamJson.toString());
        request.setReport("true"); // 需要状态报告
        if (StringUtils.isNotEmpty(uid)) {
            request.setUid(uid);
        }
        
        return sendSmsRequest(request);
    }
    
    // ========== 异步方法 ==========
    
    /**
     * 异步发送单条变量短信
     *
     * @param phone 手机号码
     * @param templateType 短信模板类型
     * @param variables 变量参数
     * @return 异步发送结果
     */
    @Async("smsTaskExecutor")
    public CompletableFuture<SmsResponse> sendSimpleSmsAsync(String phone, SmsTemplateType templateType, String... variables) {
        try {
            SmsResponse response = sendSimpleSms(phone, templateType, variables);
            return CompletableFuture.completedFuture(response);
        } catch (Exception e) {
            log.error("异步发送简单短信失败，手机号: {}", maskPhone(phone), e);
            CompletableFuture<SmsResponse> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }
    
    /**
     * 异步发送单条变量短信
     *
     * @param phone 手机号码
     * @param templateType 短信模板类型
     * @param uid 业务系统内的ID
     * @param variables 变量参数
     * @return 异步发送结果
     */
    @Async("smsTaskExecutor")
    public CompletableFuture<SmsResponse> sendSmsAsync(String phone, SmsTemplateType templateType, String uid, String... variables) {
        try {
            SmsResponse response = sendSms(phone, templateType, uid, variables);
            return CompletableFuture.completedFuture(response);
        } catch (Exception e) {
            log.error("异步发送短信失败，手机号: {}, 业务ID: {}", maskPhone(phone), uid, e);
            CompletableFuture<SmsResponse> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }
    
    /**
     * 异步批量发送变量短信
     *
     * @param phoneAndVariables 手机号和变量列表
     * @param templateType 短信模板类型
     * @param uid 业务系统内的ID
     * @return 异步发送结果
     */
    @Async("smsTaskExecutor")
    public CompletableFuture<SmsResponse> sendBatchSmsAsync(List<String> phoneAndVariables, SmsTemplateType templateType, String uid) {
        try {
            SmsResponse response = sendBatchSms(phoneAndVariables, templateType, uid);
            return CompletableFuture.completedFuture(response);
        } catch (Exception e) {
            log.error("异步批量发送短信失败，业务ID: {}, 数量: {}", uid, phoneAndVariables != null ? phoneAndVariables.size() : 0, e);
            CompletableFuture<SmsResponse> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }
    
    /**
     * 异步发送短信（仅记录日志，不返回结果）
     * 适用于不关心发送结果的业务场景
     *
     * @param phone 手机号码
     * @param templateType 短信模板类型
     * @param uid 业务系统内的ID
     * @param variables 变量参数
     */
    @Async("smsTaskExecutor")
    public void sendSmsAsyncVoid(String phone, SmsTemplateType templateType, String uid, String... variables) {
        try {
            SmsResponse response = sendSms(phone, templateType, uid, variables);
            if (response.isSuccess()) {
                log.info("异步短信发送成功，手机号: {}, 业务ID: {}, 消息ID: {}", 
                    maskPhone(phone), uid, response.getMsgId());
            } else {
                log.error("异步短信发送失败，手机号: {}, 业务ID: {}, 错误码: {}, 错误信息: {}", 
                    maskPhone(phone), uid, response.getCode(), response.getErrorMsg());
            }
        } catch (Exception e) {
            log.error("异步发送短信异常，手机号: {}, 业务ID: {}", maskPhone(phone), uid, e);
        }
    }
    
    /**
     * 发送短信请求
     *
     * @param request 短信请求对象
     * @return 发送结果
     */
    private SmsResponse sendSmsRequest(SmsRequest request) {
        // 检查是否启用短信服务
        if (!enabled) {
            log.info("短信服务已禁用，跳过发送: 手机号={}, 模板ID={}", request.getPhoneNumbers(), request.getTemplateId());
            return createMockResponse();
        }
        
        if (StringUtils.isEmpty(account)) {
            log.error("短信配置未设置，account: {}", account);
            throw new ServiceException("短信服务配置错误，请检查account配置");
        }
        
        if (!encrypt && StringUtils.isEmpty(password)) {
            log.error("未开启加密鉴权时password为必传");
            throw new ServiceException("短信服务配置错误，未开启加密时password为必传");
        }
        
        try {
            log.info("发送短信请求: 手机号={}, 模板ID={}", maskPhoneNumbers(request.getPhoneNumbers()), request.getTemplateId());
            
            String requestJson = JacksonUtil.toJSONString(request);
            log.debug("短信请求JSON: {}", requestJson);
            
            String responseJson;
            
            // 如果开启加密鉴权，生成签名并添加到Header
            if (encrypt) {
                String signature = makeSignature(password, request.getTimestamp(), request.getNonce());
                // 清空request中的password，因为已经通过Header传递签名
                request.setPassword(null);
                String finalRequestJson = JacksonUtil.toJSONString(request);
                
                responseJson = OkHttps.sync(SMS_API_URL)
                        .bodyType(OkHttps.JSON)
                        .addHeader("Content-Type", "application/json;charset=UTF-8")
                        .addHeader("X-QA-Hmac-Signature", signature)
                        .setBodyPara(finalRequestJson)
                        .post()
                        .getBody()
                        .toString();
            } else {
                // 未开启加密鉴权时，Header传空字符串
                responseJson = OkHttps.sync(SMS_API_URL)
                        .bodyType(OkHttps.JSON)
                        .addHeader("Content-Type", "application/json;charset=UTF-8")
                        .addHeader("X-QA-Hmac-Signature", "")
                        .setBodyPara(requestJson)
                        .post()
                        .getBody()
                        .toString();
            }
            
            log.info("短信响应: {}", responseJson);
            
            SmsResponse response = JacksonUtil.parseObject(SmsResponse.class, responseJson);
            
            if (!response.isSuccess()) {
                String errorDescription = getErrorDescription(response.getCode());
                log.error("短信发送失败: code={}, errorMsg={}, 错误说明: {}", 
                    response.getCode(), response.getErrorMsg(), errorDescription);
            } else {
                log.info("短信发送成功: msgId={}, successNum={}", response.getMsgId(), response.getSuccessNum());
            }
            
            return response;
            
        } catch (Exception e) {
            log.error("发送短信异常", e);
            throw new ServiceException("短信发送失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成HmacSHA256签名
     * 
     * @param password API密码
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @return 签名字符串
     */
    private String makeSignature(String password, String timestamp, String nonce) {
        // md5Password=API密码的MD5加密（32位小写值）
        String md5Password = DigestUtils.md5Hex(password);
        
        // 生成待签名字符串
        String str = generateStr(md5Password, timestamp, nonce);
        
        // 使用HmacSHA256进行签名
        return HmacUtils.hmacSha256Hex(md5Password, str.replaceAll("\\s+", ""));
    }
    
    /**
     * 签名待处理的字符串拼接
     * md5Password + timestamp + nonce 按字典序排序后拼接
     */
    private String generateStr(String md5Password, String timestamp, String nonce) {
        String[] array = new String[]{md5Password, timestamp, nonce};
        StringBuilder sb = new StringBuilder();
        // 字符串排序
        Arrays.sort(array);
        for (int i = 0; i < 3; i++) {
            sb.append(array[i]);
        }
        return sb.toString();
    }
    
    /**
     * 构建模板参数JSON
     * 
     * @param variables 变量参数，每个参数会自动去除前后空格并限制长度不超过8个字符
     * @return JSON格式的参数字符串
     */
    private String buildTemplateParamJson(String... variables) {
        if (variables == null || variables.length == 0) {
            return null;
        }
        
        StringBuilder json = new StringBuilder("[{");
        for (int i = 0; i < variables.length; i++) {
            if (i > 0) json.append(",");
            
            String param = variables[i];
            if (param != null) {
                // 去除前后空格
                param = param.trim();
                // 限制长度不超过8个字符
                if (param.length() > 8) {
                    param = param.substring(0, 8);
                }
            } else {
                param = "";
            }
            
            json.append("\"param").append(i + 1).append("\":\"").append(param).append("\"");
        }
        json.append("}]");
        return json.toString();
    }
    
    /**
     * 创建模拟响应（用于禁用短信服务时）
     */
    private SmsResponse createMockResponse() {
        SmsResponse mockResponse = new SmsResponse();
        mockResponse.setCode("000000");
        mockResponse.setErrorMsg("");
        mockResponse.setMsgId("MOCK_" + System.currentTimeMillis());
        mockResponse.setSuccessNum("1");
        mockResponse.setFailNum("0");
        mockResponse.setTime(String.valueOf(System.currentTimeMillis()));
        return mockResponse;
    }
    
    /**
     * 根据错误码获取错误描述
     * 基于创蓝云智文档的错误码表
     */
    private String getErrorDescription(String errorCode) {
        if (errorCode == null) {
            return "未知错误";
        }
        
        switch (errorCode) {
            case "000000": return "提交成功";
            case "101": return "无此用户";
            case "102": return "密码错";
            case "103": return "提交过快（提交速度超过流速限制）";
            case "104": return "系统忙（因为提交量过大，数据库繁忙）";
            case "105": return "敏感短信（短信内容包含敏感词）";
            case "106": return "消息长度错（>536或<=0）";
            case "107": return "包含错误的手机号码";
            case "108": return "手机号码个数错（手机号包含了中文符号；手机号个数错了，群发>1000 或<=0）";
            case "109": return "无发送额度（当前使用的API账号下没有发送额度）";
            case "110": return "不在发送时间内";
            case "111": return "超出该账户当月发送额度限制";
            case "112": return "产品错误（通道出现异常）";
            case "113": return "扩展码格式错（非数字或者长度不对）";
            case "114": return "可用参数组个数错误（msg参数的变量符号固定使用\"{$var}\"；变量符号在20个以内）";
            case "116": return "签名不合法或未带签名（短信签名需要报备通过后才能使用）";
            case "117": return "IP地址认证错（登录控制台在对应使用的API账号下加白ip）";
            case "118": return "用户没有相应的发送权限（账号被禁止发送）";
            case "119": return "用户已过期";
            case "120": return "违反防盗用策略（日发送限制）";
            case "123": return "发送类型错误（cmpp协议的账户不能使用https协议方式）";
            case "124": return "白模板匹配错误（接口传递的内容与报备的模板内容要完全一致，包括标点符号）";
            case "125": return "匹配驳回模板，提交失败";
            case "127": return "定时发送时间格式错误（格式为 yyyyMMddHHmm）";
            case "128": return "内容编码失败";
            case "129": return "JSON格式错误";
            case "130": return "请求参数错误（缺少必填参数）";
            case "132": return "消息长度错（>3500或<=0），超过短信最大支持字数";
            case "133": return "单一手机号错误";
            case "134": return "违反防盗策略，超过月发送限制";
            case "135": return "超过同一手机号相同内容发送限制";
            case "136": return "不可批量提交\"验证码\"短信";
            case "139": return "超出安全发送时间（时间戳过期，时间戳时间跟请求接口的时间差异控制在30s以内）";
            case "140": return "短信内容解密错误（密钥没有使用正确）";
            case "144": return "产品未上线限制日发送数量（签名报备选择的未上线会日限100条）";
            case "145": return "验签失败";
            case "152": return "模板不存在";
            case "153": return "消息长度错（>2000或者≤0）";
            case "154": return "长短信拼接错误";
            case "155": return "转发失败";
            case "158": return "退订语不符合规范，退订语现在只支持\"拒收请回复R\"";
            case "159": return "触发反轰炸策略";
            default: return "未知错误码: " + errorCode;
        }
    }
    
    /**
     * 构建订单通知短信参数
     *
     * @param phone 手机号
     * @param buyerName 买家姓名
     * @param productName 商品名称
     * @return 参数字符串
     */
    public static String buildOrderNotifyParams(String phone, String buyerName, String productName) {
        return phone + "," + buyerName + "," + productName;
    }
    
    /**
     * 构建关注用户新商品通知参数
     *
     * @param phone 手机号
     * @param productName 商品名称
     * @return 参数字符串
     */
    public static String buildFollowNotifyParams(String phone, String productName) {
        return phone + "," + productName;
    }
    
    /**
     * 构建退款通知参数
     *
     * @param phone 手机号
     * @param productName 商品名称
     * @param refundAmount 退款金额
     * @return 参数字符串
     */
    public static String buildRefundNotifyParams(String phone, String productName, String refundAmount) {
        return phone + "," + productName + "," + refundAmount;
    }

    /**
     * 脱敏手机号显示
     * 
     * @param phone 原始手机号
     * @return 脱敏后的手机号
     */
    private String maskPhone(String phone) {
        if (phone == null || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }
    
    /**
     * 脱敏手机号列表显示（处理包含多个手机号的字符串）
     * 
     * @param phoneNumbers 原始手机号列表字符串
     * @return 脱敏后的手机号列表字符串
     */
    private String maskPhoneNumbers(String phoneNumbers) {
        if (phoneNumbers == null || phoneNumbers.isEmpty()) {
            return phoneNumbers;
        }
        
        String[] phones = phoneNumbers.split(",");
        StringBuilder maskedPhones = new StringBuilder();
        
        for (int i = 0; i < phones.length; i++) {
            if (i > 0) {
                maskedPhones.append(",");
            }
            maskedPhones.append(maskPhone(phones[i].trim()));
        }
        
        return maskedPhones.toString();
    }
} 