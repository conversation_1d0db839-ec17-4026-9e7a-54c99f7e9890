package com.fs.swap.system.service.impl;

import java.util.*;
import com.fs.swap.common.utils.DateUtils;
import com.fs.swap.common.utils.uuid.IdUtils;
import com.fs.swap.system.mapper.UserInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fs.swap.system.mapper.MessageMapper;
import com.fs.swap.common.core.domain.entity.Message;
import com.fs.swap.common.core.domain.entity.UserInfo;
import com.fs.swap.system.service.IMessageService;
import com.fs.swap.system.service.IConversationService;
import com.fs.swap.system.service.IConversationMemberService;
import com.fs.swap.common.core.domain.dto.ConversationListDTO;
import com.fs.swap.common.core.domain.dto.UnreadCountDTO;

/**
 * 消息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024
 */
@Service
public class MessageServiceImpl implements IMessageService 
{
    @Autowired
    private MessageMapper messageMapper;
    
    @Autowired
    private UserInfoMapper userInfoMapper;

    @Autowired
    private IConversationService conversationService;
    
    @Autowired
    private IConversationMemberService conversationMemberService;

    /**
     * 查询消息
     * 
     * @param id 消息主键
     * @return 消息
     */
    @Override
    public Message selectMessageById(String id)
    {
        return messageMapper.selectMessageById(id);
    }

    /**
     * 查询消息列表
     * 
     * @param message 消息
     * @return 消息集合
     */
    @Override
    public List<Message> selectMessageList(Message message)
    {
        return messageMapper.selectMessageList(message);
    }

    /**
     * 查询用户消息列表（用于PageHelper分页）
     * 
     * @param userId 用户ID
     * @param type 消息类型
     * @return 消息集合
     */
    @Override
    public List<Message> selectUserMessageList(Long userId, String type)
    {
        return messageMapper.selectUserMessageList(userId, type);
    }

    /**
     * 分页查询用户消息列表
     * 
     * @param userId 用户ID
     * @param type 消息类型
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 消息集合
     */
    @Override
    public List<Message> selectUserMessageListWithPage(Long userId, String type, int pageNum, int pageSize)
    {
        int offset = (pageNum - 1) * pageSize;
        return messageMapper.selectUserMessageListWithPage(userId, type, offset, pageSize);
    }

    /**
     * 获取用户消息总数
     * 
     * @param userId 用户ID
     * @param type 消息类型
     * @return 总数
     */
    @Override
    public long countUserMessages(Long userId, String type)
    {
        return messageMapper.countUserMessages(userId, type);
    }

    /**
     * 新增消息
     * 
     * @param message 消息
     * @return 结果
     */
    @Override
    public int insertMessage(Message message)
    {
        if (message.getId() == null || message.getId().isEmpty()) {
            message.setId(IdUtils.fastSimpleUUID());
        }
        message.setCreateTime(DateUtils.getNowDate());
        return messageMapper.insertMessage(message);
    }

    /**
     * 修改消息
     * 
     * @param message 消息
     * @return 结果
     */
    @Override
    public int updateMessage(Message message)
    {
        message.setUpdateTime(DateUtils.getNowDate());
        return messageMapper.updateMessage(message);
    }

    /**
     * 批量删除消息
     * 
     * @param ids 需要删除的消息主键
     * @return 结果
     */
    @Override
    public int deleteMessageByIds(String[] ids)
    {
        return messageMapper.deleteMessageByIds(ids);
    }

    /**
     * 删除消息信息
     * 
     * @param id 消息主键
     * @return 结果
     */
    @Override
    public int deleteMessageById(String id)
    {
        return messageMapper.deleteMessageById(id);
    }

    /**
     * 获取用户未读消息数量统计
     * 
     * @param userId 用户ID
     * @return 未读消息统计
     */
    @Override
    public Map<String, Integer> getUserUnreadCount(Long userId)
    {
        List<UnreadCountDTO> resultList = messageMapper.getUserUnreadCount(userId);
        Map<String, Integer> unreadCount = new HashMap<>();
        
        // 初始化所有类型为0
        unreadCount.put("system", 0);
        unreadCount.put("chat", 0);
        unreadCount.put("activity", 0);
        unreadCount.put("order", 0);
        unreadCount.put("total", 0);
        
        int total = 0;
        for (UnreadCountDTO result : resultList) {
            String type = result.getType();
            Integer count = result.getCount();
            unreadCount.put(type, count);
            total += count;
        }
        unreadCount.put("total", total);
        
        return unreadCount;
    }

    /**
     * 标记消息为已读
     * 
     * @param messageIds 消息ID列表
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int markMessagesAsRead(List<String> messageIds, Long userId)
    {
        if (messageIds == null || messageIds.isEmpty()) {
            return 0;
        }
        
        // 先获取要标记的消息详情，以便获取会话ID
        List<Message> messages = messageMapper.selectMessagesByIds(messageIds);
        
        // 标记消息为已读
        int result = messageMapper.markMessagesAsRead(messageIds, userId);
        
        // 清零相关会话的未读计数
        Set<String> conversationIds = new HashSet<>();
        for (Message message : messages) {
            if (message.getConversationId() != null && !message.getConversationId().isEmpty()) {
                conversationIds.add(message.getConversationId());
            }
        }
        
        // 对每个会话清零未读计数
        for (String conversationId : conversationIds) {
            try {
                // 获取该会话中最新消息ID
                String lastMessageId = messageMapper.getLastMessageIdInConversation(conversationId);
                conversationMemberService.clearUnreadCount(conversationId, userId, lastMessageId);
            } catch (Exception e) {
                // 记录日志但不影响主流程
                System.err.println("清零会话未读计数失败: conversationId=" + conversationId + ", error=" + e.getMessage());
            }
        }
        
        return result;
    }

    /**
     * 批量标记某类型消息为已读
     * 
     * @param userId 用户ID
     * @param type 消息类型
     * @return 结果
     */
    @Override
    public int markAllMessagesAsRead(Long userId, String type)
    {
        // 如果是聊天消息，需要清零所有会话的未读计数
        if ("chat".equals(type)) {
            // 获取用户参与的所有会话
            List<com.fs.swap.common.core.domain.entity.ConversationMember> userConversations = 
                conversationMemberService.selectConversationsByUser(userId);
            
            // 清零所有会话的未读计数
            for (com.fs.swap.common.core.domain.entity.ConversationMember member : userConversations) {
                try {
                    String conversationId = member.getConversationId();
                    String lastMessageId = messageMapper.getLastMessageIdInConversation(conversationId);
                    conversationMemberService.clearUnreadCount(conversationId, userId, lastMessageId);
                } catch (Exception e) {
                    // 记录日志但不影响主流程
                    System.err.println("清零会话未读计数失败: error=" + e.getMessage());
                }
            }
        }
        
        return messageMapper.markAllMessagesAsRead(userId, type);
    }

    /**
     * 发送系统通知
     * 
     * @param title 标题
     * @param content 内容
     * @param type 消息类型
     * @param targetUserIds 目标用户ID列表，为空表示发送给所有用户
     * @param jumpType 跳转类型
     * @param jumpUrl 跳转URL
     * @param relatedId 关联ID
     * @param actionText 操作按钮文本
     * @return 结果
     */
    @Override
    public int sendSystemNotification(String title, String content, String type, 
                                    List<Long> targetUserIds, String jumpType, 
                                    String jumpUrl, String relatedId, String actionText)
    {
        List<Message> messages = new ArrayList<>();
        List<Long> userIds;
        
        if (targetUserIds == null || targetUserIds.isEmpty()) {
            // 获取所有用户ID
            userIds = userInfoMapper.selectAllUserIds();
        } else {
            userIds = targetUserIds;
        }
        
        Date now = DateUtils.getNowDate();
        for (Long userId : userIds) {
            Message message = new Message();
            message.setId(IdUtils.fastSimpleUUID());
            message.setUserId(userId);
            message.setType(type);
            message.setTitle(title);
            message.setContent(content);
            message.setAvatar("/static/img/system-avatar.png"); // 系统消息默认头像
            message.setIsRead(0); // 未读
            message.setJumpType(jumpType);
            message.setJumpUrl(jumpUrl);
            message.setRelatedId(relatedId);
            message.setActionText(actionText);
            message.setStatus(0); // 正常状态
            message.setCreateTime(now);
            messages.add(message);
        }
        
        if (!messages.isEmpty()) {
            return messageMapper.batchInsertMessages(messages);
        }
        return 0;
    }

    /**
     * 发送用户消息（重构后支持会话表）
     * 
     * @param fromUserId 发送者用户ID
     * @param toUserId 接收者用户ID
     * @param content 消息内容
     * @param conversationId 会话ID
     * @return 结果
     */
    public int sendUserMessage(Long fromUserId, Long toUserId, String content, String conversationId)
    {
        // 兼容旧签名，默认不带商品ID
        return sendUserMessage(fromUserId, toUserId, content, conversationId, null);
    }

    /**
     * 新签名，支持附带商品ID
     */
    @Override
    public int sendUserMessage(Long fromUserId, Long toUserId, String content, String conversationId, Long productId)
    {
        // 获取发送者信息
        UserInfo fromUser = userInfoMapper.selectUserInfoById(fromUserId);
        if (fromUser == null) {
            return 0;
        }
        
        // 确保会话存在（如果不存在则创建）
        if (!conversationService.checkConversationExists(conversationId)) {
            // 如果会话不存在，使用会话服务创建
            String newConversationId = conversationService.createOrGetSingleConversation(fromUserId, toUserId);
            if (!newConversationId.equals(conversationId)) {
                // 如果生成的会话ID与传入的不一致，说明可能存在问题
                conversationId = newConversationId;
            }
        }
        
        // 创建消息记录
        Message message = new Message();
        message.setId(IdUtils.fastSimpleUUID());
        message.setUserId(toUserId);
        message.setFromUserId(fromUserId);
        message.setType("chat");
        message.setTitle(fromUser.getNickname());
        message.setContent(content);
        message.setAvatar(fromUser.getAvatar());
        message.setIsRead(0); // 未读
        message.setConversationId(conversationId);
        // 将商品ID写入关联ID
        if (productId != null) {
            message.setRelatedId(String.valueOf(productId));
        }
        message.setJumpType("page");
        String baseUrl = "/pages/conversation/detail/index?conversationId=" + conversationId;
        if (productId != null) {
            baseUrl += "&productId=" + productId;
        }
        message.setJumpUrl(baseUrl);
        message.setStatus(0); // 正常状态
        
        int result = insertMessage(message);
        
        if (result > 0) {
            // 更新会话最后消息信息
            conversationService.updateLastMessage(conversationId, message.getId(), content, message.getCreateTime());
            
            // 增加接收者的未读消息数量
            conversationMemberService.incrementUnreadCount(conversationId, toUserId, 1);
        }
        
        return result;
    }

    /**
     * 搜索消息
     * 
     * @param userId 用户ID
     * @param keyword 关键词
     * @param type 消息类型
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 消息集合
     */
    @Override
    public List<Message> searchMessages(Long userId, String keyword, String type, int pageNum, int pageSize)
    {
        int offset = (pageNum - 1) * pageSize;
        return messageMapper.searchMessages(userId, keyword, type, offset, pageSize);
    }

    /**
     * 搜索消息（用于PageHelper分页）
     * 
     * @param userId 用户ID
     * @param keyword 关键词
     * @param type 消息类型
     * @return 消息集合
     */
    @Override
    public List<Message> searchMessages(Long userId, String keyword, String type)
    {
        return messageMapper.searchMessagesList(userId, keyword, type);
    }

    /**
     * 撤回消息
     * 
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int recallMessage(String messageId, Long userId)
    {
        return messageMapper.recallMessage(messageId, userId);
    }

    /**
     * 获取会话消息列表
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 消息列表
     */
    @Override
    public List<Message> getConversationMessages(String conversationId, Long userId)
    {
        return messageMapper.getConversationMessages(conversationId, userId);
    }

    /**
     * 获取会话中的未读消息
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 未读消息列表
     */
    @Override
    public List<Message> getUnreadConversationMessages(String conversationId, Long userId)
    {
        return messageMapper.getUnreadConversationMessages(conversationId, userId);
    }
} 