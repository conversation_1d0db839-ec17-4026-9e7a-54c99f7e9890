package com.fs.swap.system.service;

import java.util.List;
import java.util.Map;
import com.fs.swap.common.core.domain.entity.Message;

/**
 * 消息Service接口
 * 
 * <AUTHOR>
 * @date 2024
 */
public interface IMessageService 
{
    /**
     * 查询消息
     * 
     * @param id 消息主键
     * @return 消息
     */
    public Message selectMessageById(String id);

    /**
     * 查询消息列表
     * 
     * @param message 消息
     * @return 消息集合
     */
    public List<Message> selectMessageList(Message message);

    /**
     * 查询用户消息列表（用于PageHelper分页）
     * 
     * @param userId 用户ID
     * @param type 消息类型
     * @return 消息集合
     */
    public List<Message> selectUserMessageList(Long userId, String type);

    /**
     * 分页查询用户消息列表
     * 
     * @param userId 用户ID
     * @param type 消息类型
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 消息集合
     */
    public List<Message> selectUserMessageListWithPage(Long userId, String type, int pageNum, int pageSize);

    /**
     * 获取用户消息总数
     * 
     * @param userId 用户ID
     * @param type 消息类型
     * @return 总数
     */
    public long countUserMessages(Long userId, String type);

    /**
     * 新增消息
     * 
     * @param message 消息
     * @return 结果
     */
    public int insertMessage(Message message);

    /**
     * 修改消息
     * 
     * @param message 消息
     * @return 结果
     */
    public int updateMessage(Message message);

    /**
     * 批量删除消息
     * 
     * @param ids 需要删除的消息主键集合
     * @return 结果
     */
    public int deleteMessageByIds(String[] ids);

    /**
     * 删除消息信息
     * 
     * @param id 消息主键
     * @return 结果
     */
    public int deleteMessageById(String id);

    /**
     * 获取用户未读消息数量统计
     * 
     * @param userId 用户ID
     * @return 未读消息统计
     */
    public Map<String, Integer> getUserUnreadCount(Long userId);

    /**
     * 标记消息为已读
     * 
     * @param messageIds 消息ID列表
     * @param userId 用户ID
     * @return 结果
     */
    public int markMessagesAsRead(List<String> messageIds, Long userId);

    /**
     * 批量标记某类型消息为已读
     * 
     * @param userId 用户ID
     * @param type 消息类型
     * @return 结果
     */
    public int markAllMessagesAsRead(Long userId, String type);

    /**
     * 发送系统通知
     * 
     * @param title 标题
     * @param content 内容
     * @param type 消息类型
     * @param targetUserIds 目标用户ID列表，为空表示发送给所有用户
     * @param jumpType 跳转类型
     * @param jumpUrl 跳转URL
     * @param relatedId 关联ID
     * @param actionText 操作按钮文本
     * @return 结果
     */
    public int sendSystemNotification(String title, String content, String type, 
                                    List<Long> targetUserIds, String jumpType, 
                                    String jumpUrl, String relatedId, String actionText);

    /**
     * 发送用户消息（重构后支持会话表）
     * 
     * @param fromUserId 发送者用户ID
     * @param toUserId 接收者用户ID
     * @param content 消息内容
     * @param conversationId 会话ID
     * @param productId 商品ID（可选）
     * @return 结果
     */
    public int sendUserMessage(Long fromUserId, Long toUserId, String content, String conversationId, Long productId);

    /**
     * 搜索消息
     * 
     * @param userId 用户ID
     * @param keyword 关键词
     * @param type 消息类型
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 消息集合
     */
    public List<Message> searchMessages(Long userId, String keyword, String type, int pageNum, int pageSize);

    /**
     * 搜索消息（用于PageHelper分页）
     * 
     * @param userId 用户ID
     * @param keyword 关键词
     * @param type 消息类型
     * @return 消息集合
     */
    public List<Message> searchMessages(Long userId, String keyword, String type);

    /**
     * 撤回消息
     * 
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 结果
     */
    public int recallMessage(String messageId, Long userId);

    /**
     * 获取会话消息列表
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 消息列表
     */
    public List<Message> getConversationMessages(String conversationId, Long userId);

    /**
     * 获取会话中的未读消息
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 未读消息列表
     */
    public List<Message> getUnreadConversationMessages(String conversationId, Long userId);
} 