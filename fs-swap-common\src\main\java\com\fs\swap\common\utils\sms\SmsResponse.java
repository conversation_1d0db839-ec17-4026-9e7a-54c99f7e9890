package com.fs.swap.common.utils.sms;

import com.fasterxml.jackson.annotation.JsonAlias;

/**
 * 短信发送响应对象
 * 基于创蓝云智最新API响应结构
 */
public class SmsResponse {
    
    /**
     * 提交响应状态码，返回"000000"表示提交成功
     */
    private String code;
    
    /**
     * 提交响应状态码中文说明（提交成功返回空）
     */
    private String errorMsg;
    
    /**
     * 消息id（32位纯数字）
     */
    private String msgId;
    
    /**
     * 提交成功数量，参数校验失败时不返回
     */
    private String successNum;
    
    /**
     * 提交失败数量，参数校验失败时不返回
     */
    private String failNum;
    
    /**
     * 响应时间
     */
    private String time;

    public SmsResponse() {
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    /**
     * 兼容旧版本msgid字段
     */
    public String getMsgid() {
        return msgId;
    }

    public void setMsgid(String msgid) {
        this.msgId = msgid;
    }

    public String getSuccessNum() {
        return successNum;
    }

    public void setSuccessNum(String successNum) {
        this.successNum = successNum;
    }

    public String getFailNum() {
        return failNum;
    }

    public void setFailNum(String failNum) {
        this.failNum = failNum;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    /**
     * 判断是否发送成功
     * 返回"000000"表示提交成功
     */
    public boolean isSuccess() {
        return "000000".equals(code);
    }

    @Override
    public String toString() {
        return "SmsResponse{" +
                "code='" + code + '\'' +
                ", errorMsg='" + errorMsg + '\'' +
                ", msgId='" + msgId + '\'' +
                ", successNum='" + successNum + '\'' +
                ", failNum='" + failNum + '\'' +
                ", time='" + time + '\'' +
                '}';
    }
} 