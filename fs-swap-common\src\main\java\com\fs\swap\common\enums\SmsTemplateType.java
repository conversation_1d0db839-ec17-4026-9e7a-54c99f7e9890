package com.fs.swap.common.enums;

/**
 * 短信模板类型枚举
 * 变量格式使用创蓝云智规范：{s}
 */
public enum SmsTemplateType implements BaseEnum {
    /**
     * 下单通知
     */
    ORDER_CREATED("ORDER_CREATED", "1020174680", "【苏州趣换网络】"),
    
    /**
     * 订单完成通知(买家)
     */
    ORDER_COMPLETED_BUY("ORDER_COMPLETED_BUY", "1020167977", "【苏州趣换网络】"),
    
    /**
     * 订单完成通知(卖家)
     */
    ORDER_COMPLETED_SELL("ORDER_COMPLETED_SELL", "1020167609", "【苏州趣换网络】"),
    
    /**
     * 关注用户发布新商品通知
     */
    FOLLOW_USER_NEW_PRODUCT("FOLLOW_USER_NEW_PRODUCT", "1021004", "【趣换网络"),

    /**
     * 订单取消通知
     */
    ORDER_CANCELLED("ORDER_CANCELLED", "1021005", "【趣换网络】"),

    /**
     * 退款申请通知
     */
    REFUND_APPLY("REFUND_APPLY", "1021006", "【趣换网络】"),

    /**
     * 退款成功通知
     */
    REFUND_SUCCESS("REFUND_SUCCESS", "1021007", "【趣换网络】");

    private final String code;
    private final String templateId;
    private final String signature;

    SmsTemplateType(String code, String templateId, String template) {
        this.code = code;
        this.templateId = templateId;
        this.signature = template;
    }

    public String getCode() {
        return code;
    }

    public String getTemplateId() {
        return templateId;
    }

    public String getSignature() {
        return signature;
    }

    @Override
    public String val() {
        return code;
    }

    /**
     * 根据代码获取短信模板类型
     *
     * @param code 代码
     * @return 短信模板类型枚举
     */
    public static SmsTemplateType getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (SmsTemplateType type : SmsTemplateType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据模板ID获取短信模板类型
     *
     * @param templateId 模板ID
     * @return 短信模板类型枚举
     */
    public static SmsTemplateType getByTemplateId(String templateId) {
        if (templateId == null) {
            return null;
        }
        for (SmsTemplateType type : SmsTemplateType.values()) {
            if (type.getTemplateId().equals(templateId)) {
                return type;
            }
        }
        return null;
    }
} 